2025-07-28 06:42:46 - root - INFO - 简化日志系统初始化完成
2025-07-28 06:42:46 - main - INFO - 应用程序启动
2025-07-28 06:42:46 - __main__ - INFO - Qt应用程序已创建
2025-07-28 06:42:46 - UnifiedConfigManager - INFO - 配置加载成功: app_config.json
2025-07-28 06:42:46 - __main__ - INFO - 统一配置管理器已创建
2025-07-28 06:42:46 - DatabaseManager - INFO - SQLite连接已配置WAL模式
2025-07-28 06:42:46 - DatabaseManager - ERROR - 检查表结构失败: 1
2025-07-28 06:42:46 - DatabaseManager - INFO - 数据库表结构创建完成
2025-07-28 06:42:46 - DatabaseManager - INFO - 数据库初始化完成: E:\VScodexiangmu\insleidian2\emulator_system.db
2025-07-28 06:42:46 - EmulatorRepository - INFO - 模拟器数据仓库初始化完成
2025-07-28 06:42:46 - StartupManager - INFO - 统一启动管理器初始化完成
2025-07-28 06:42:46 - TaskActivityHeartbeatManager - INFO - 心跳检测已启用
2025-07-28 06:42:46 - TaskActivityHeartbeatManager - INFO - 任务活动检测心跳管理器初始化完成
2025-07-28 06:42:46 - TaskActivityHeartbeatManager - INFO - 心跳监控服务已启动，检测间隔: 20秒
2025-07-28 06:42:46 - UnifiedEmulatorManager - INFO - 统一模拟器管理器初始化完成
2025-07-28 06:42:46 - __main__ - INFO - 修复版异步桥梁已创建
2025-07-28 06:42:46 - BasicConfigUI - INFO - 基础配置加载完成
2025-07-28 06:42:46 - BasicConfigUI - INFO - 基础配置信号连接完成
2025-07-28 06:42:46 - BasicConfigUI - INFO - 基础配置UI已注册为配置观察者，支持热加载
2025-07-28 06:42:46 - BasicConfigUI - INFO - 浏览按钮事件连接完成
2025-07-28 06:42:46 - BasicConfigUI - INFO - 基础配置UI初始化完成
2025-07-28 06:42:46 - InstagramDMUI - INFO - Instagram私信配置加载完成
2025-07-28 06:42:47 - InstagramFollowUI - INFO - Instagram关注配置加载完成
2025-07-28 06:42:47 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 程序启动)
2025-07-28 06:42:47 - MainWindowV2 - INFO - 事件驱动日志更新已启动 - 表格运行日志列实时更新（替代定时器）
2025-07-28 06:42:47 - MainWindowV2 - INFO - Instagram任务状态更新信号已连接
2025-07-28 06:42:47 - __main__ - INFO - UI主窗口已创建
2025-07-28 06:42:47 - MainWindowV2 - INFO - UI主窗口已创建
2025-07-28 06:42:47 - __main__ - INFO - 主窗口已显示
2025-07-28 06:42:47 - WindowArrangementManager - INFO - 窗口排列管理器初始化完成
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 任务完成信号已连接到窗口排列管理器
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 任务完成信号、状态变化信号、心跳监控信号和窗口排列信号已连接到UI
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 采用事件驱动方式，无需监控器
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 06:42:47 - AsyncEventLoopManager - INFO - 专用异步事件循环已启动
2025-07-28 06:42:47 - __main__ - INFO - UI层和业务层已连接
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 06:42:47 - __main__ - INFO - 启动Qt事件循环
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 06:42:47 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 06:42:47 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 06:42:47 - MainWindowV2 - INFO - 开始扫描模拟器 (来源: 路径变化)
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 执行异步操作: scan_emulators
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 异步桥接器: 开始处理扫描请求
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 异步桥接器: 扫描路径 = G:/leidian/LDPlayer9
2025-07-28 06:42:47 - App - INFO - 开始快速扫描模拟器，路径: G:/leidian/LDPlayer9, 检查状态: False
2025-07-28 06:42:47 - UnifiedConfigManager - INFO - 配置保存成功: app_config.json
2025-07-28 06:42:47 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 06:42:47 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 06:42:47 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 06:42:47 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 06:42:47 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 06:42:47 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.25s | count: 1229
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 06:42:47 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 06:42:47 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 06:42:47 - App - INFO - ldconsole命令执行成功，输出长度: 48410
2025-07-28 06:42:47 - DatabaseManager - INFO - 查询执行完成: 1229行
2025-07-28 06:42:47 - DatabaseManager - INFO - 事务执行成功: 1229个操作
2025-07-28 06:42:47 - DatabaseManager - INFO - 模拟器数据同步完成: 1229个模拟器
2025-07-28 06:42:47 - EmulatorRepository - INFO - 模拟器数据同步完成: 1229个
2025-07-28 06:42:47 - Performance - INFO - 快速模拟器扫描完成 | 耗时: 0.23s | count: 1229
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 异步桥接器: 扫描完成，结果数量 = 1229
2025-07-28 06:42:47 - FixedAsyncBridge - INFO - 异步操作完成: scan_emulators
2025-07-28 06:42:47 - MainWindowV2 - INFO - 扫描完成: 找到 1229 个模拟器
2025-07-28 06:42:47 - MainWindowV2 - INFO - 模拟器扫描完成 | count: 1229
2025-07-28 06:42:48 - ConfigHotReloadService - INFO - 配置热加载服务初始化完成（Qt定时器模式）
2025-07-28 06:42:48 - ConfigHotReloadService - INFO - 配置热加载服务已启动（Qt定时器模式）
2025-07-28 06:42:48 - FixedAsyncBridge - INFO - 后台服务已启动
2025-07-28 06:42:48 - __main__ - INFO - 后台服务已启动
2025-07-28 06:42:48 - __main__ - INFO - 延迟启动服务完成
2025-07-28 06:42:52 - FixedAsyncBridge - INFO - 执行异步操作: instagram_follow_direct_task
2025-07-28 06:42:52 - MainWindowV2 - INFO - Instagram直接关注任务已启动，涉及3个模拟器
2025-07-28 06:42:52 - FixedAsyncBridge - INFO - 异步桥接器: 处理Instagram直接关注任务请求
2025-07-28 06:42:52 - MainWindowV2 - INFO - 用户启动Instagram直接关注任务，模拟器数量: 3
2025-07-28 06:42:52 - FixedAsyncBridge - INFO - 开始处理Instagram直接关注任务（线程池模式），模拟器数量: 3
2025-07-28 06:42:52 - FixedAsyncBridge - INFO - 使用批量启动功能启动模拟器: [3, 4, 5]
2025-07-28 06:42:52 - FixedAsyncBridge - INFO - 异步桥接器: 处理批量启动请求，模拟器数量: 3
2025-07-28 06:42:52 - StartupManager - INFO - 批量启动请求 | count: 3
2025-07-28 06:42:52 - StartupManager - INFO - 启动调度器已启动
2025-07-28 06:42:52 - FixedAsyncBridge - INFO - 异步桥接器: 批量启动请求已处理，状态: queued
2025-07-28 06:42:52 - FixedAsyncBridge - INFO - 模拟器批量启动成功，开始创建关注任务线程池
2025-07-28 06:42:52 - InstagramTaskManager - INFO - Instagram任务管理器已连接模拟器状态变化信号
2025-07-28 06:42:52 - InstagramTaskManager - INFO - Instagram任务管理器初始化完成，最大并发: 2
2025-07-28 06:42:52 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器初始化完成，模式: direct
2025-07-28 06:42:52 - InstagramTaskManager - INFO - 开始启动Instagram任务管理器，模拟器数量: 3
2025-07-28 06:42:52 - InstagramFollowTaskThread - INFO - [模拟器3] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 06:42:52 - InstagramFollowTaskManager - INFO - 为模拟器 3 创建Instagram关注任务线程，模式: direct
2025-07-28 06:42:52 - InstagramFollowTaskThread - INFO - [模拟器4] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 06:42:52 - InstagramFollowTaskManager - INFO - 为模拟器 4 创建Instagram关注任务线程，模式: direct
2025-07-28 06:42:52 - InstagramFollowTaskThread - INFO - [模拟器5] Instagram关注任务线程初始化完成，模式: direct
2025-07-28 06:42:52 - InstagramFollowTaskManager - INFO - 为模拟器 5 创建Instagram关注任务线程，模式: direct
2025-07-28 06:42:52 - MainWindowV2 - INFO - 3个模拟器: 未知 -> 排队中
2025-07-28 06:42:52 - InstagramTaskManager - INFO - 线程池创建完成: 成功创建3个Instagram线程
2025-07-28 06:42:52 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 3 | starting: 0 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 0 | max_concurrent: 2
2025-07-28 06:42:52 - FixedAsyncBridge - INFO - 异步桥接器: Instagram直接关注任务请求已处理，状态: started
2025-07-28 06:42:52 - MainWindowV2 - INFO - 启动进度: 排队3个, 启动中0个, 运行0个, 失败0个 (并发槽位:0/2)
2025-07-28 06:42:52 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-28 06:42:52 - FixedAsyncBridge - INFO - 异步操作完成: start_batch
2025-07-28 06:42:52 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器3状态变化: 排队中 -> 启动中
2025-07-28 06:42:52 - MainWindowV2 - INFO - 批量启动完成: 0/3 成功
2025-07-28 06:42:52 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 3
2025-07-28 06:42:52 - MainWindowV2 - INFO - 批量启动操作完成 | success: 0 | total: 3
2025-07-28 06:42:52 - FixedAsyncBridge - INFO - 异步操作完成: instagram_follow_direct_task
2025-07-28 06:42:52 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 排队中 | new_state: 启动中
2025-07-28 06:42:52 - MainWindowV2 - INFO - 模拟器3: 排队中 -> 启动中
2025-07-28 06:42:52 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 2 | starting: 1 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 1 | max_concurrent: 2
2025-07-28 06:42:52 - MainWindowV2 - INFO - 启动进度: 排队2个, 启动中1个, 运行0个, 失败0个 (并发槽位:1/2)
2025-07-28 06:42:52 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 3
2025-07-28 06:42:57 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 06:42:57 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 排队中 | new_state: 启动中
2025-07-28 06:42:57 - InstagramTaskManager - INFO - Instagram任务管理器收到模拟器4状态变化: 排队中 -> 启动中
2025-07-28 06:42:57 - Emulator - INFO - 第 1/3 次启动尝试 | emulator_id: 4
2025-07-28 06:42:57 - MainWindowV2 - INFO - 模拟器4: 排队中 -> 启动中
2025-07-28 06:42:57 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 2 | running: 0 | failed: 0 | cancelled: 0 | completed: 0 | percentage: 0.0 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 06:42:57 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中2个, 运行0个, 失败0个 (并发槽位:2/2)
2025-07-28 06:42:57 - Emulator - INFO - 等待Android系统启动完成 | emulator_id: 4
2025-07-28 06:43:02 - Emulator - INFO - Android系统启动完成 | emulator_id: 3 | elapsed_time: 9.9秒
2025-07-28 06:43:02 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器3状态变化: 启动中 -> 运行中
2025-07-28 06:43:02 - InstagramFollowTaskManager - INFO - 启动模拟器3的Instagram关注任务线程 - 当前并发: 1/2
2025-07-28 06:43:02 - Emulator - INFO - 模拟器状态变化 | emulator_id: 3 | old_state: 启动中 | new_state: 运行中
2025-07-28 06:43:02 - Emulator - INFO - 模拟器启动成功 | emulator_id: 3 | running_count: 1
2025-07-28 06:43:02 - TaskActivityHeartbeatManager - INFO - 模拟器 3 已添加到任务活动监控，失败计数: 0
2025-07-28 06:43:02 - MainWindowV2 - INFO - 任务完成: 模拟器3, 任务start
2025-07-28 06:43:02 - MainWindowV2 - INFO - 模拟器3启动成功
2025-07-28 06:43:02 - InstagramTaskThread - INFO - [模拟器3] 开始等待启动完成
2025-07-28 06:43:02 - InstagramTaskThread - INFO - [模拟器3] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 06:43:02 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=3, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 06:43:02 - InstagramTaskThread - INFO - [模拟器3] 开始窗口排列
2025-07-28 06:43:02 - WindowArrangementManager - INFO - 模拟器3启动完成，立即触发窗口排列
2025-07-28 06:43:02 - MainWindowV2 - WARNING - 未找到模拟器3，无法更新状态
2025-07-28 06:43:02 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 06:43:02 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 1 | running: 1 | failed: 0 | cancelled: 0 | completed: 1 | percentage: 33.33333333333333 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 06:43:02 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中1个, 运行1个, 失败0个 (并发槽位:2/2)
2025-07-28 06:43:02 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中, PID: 20984
2025-07-28 06:43:02 - MainWindowV2 - INFO - 模拟器3: 启动中 -> 运行中
2025-07-28 06:43:04 - MainWindowV2 - INFO - 开始排列窗口...
2025-07-28 06:43:04 - WindowArrangementManager - INFO - 开始执行窗口排列
2025-07-28 06:43:04 - WindowArrangementManager - INFO - 窗口扫描完成，找到 1 个运行中的模拟器窗口
2025-07-28 06:43:04 - WindowArrangementManager - INFO - 找到 1 个正在运行的模拟器窗口
2025-07-28 06:43:04 - WindowArrangementManager - INFO - 屏幕分辨率: 2560x1440
2025-07-28 06:43:04 - WindowArrangementManager - INFO - 使用第一个窗口的实际大小作为网格基准: 302x435
2025-07-28 06:43:04 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 不在网格位置 (当前位置: 1149, 483)，需要排列
2025-07-28 06:43:04 - WindowArrangementManager - INFO - 需要排列 1 个窗口
2025-07-28 06:43:04 - WindowArrangementManager - INFO - 找到 1 个空闲网格位置
2025-07-28 06:43:04 - WindowArrangementManager - INFO - 窗口 '雷电模拟器-3' 已排列到网格位置 (0, 0): (0, 0) 保持原大小: 302x435
2025-07-28 06:43:04 - WindowArrangementManager - INFO - 窗口排列完成，成功排列 1/1 个窗口
2025-07-28 06:43:04 - MainWindowV2 - INFO - 窗口排列完成: 窗口排列完成，成功排列 1/1 个窗口 (成功: 1/1)
2025-07-28 06:43:06 - TaskActivityHeartbeatManager - INFO - 开始检测 1 个模拟器的心跳状态
2025-07-28 06:43:06 - FixedAsyncBridge - INFO - 执行异步操作: heartbeat_check
2025-07-28 06:43:06 - UnifiedEmulatorManager - INFO - 心跳检测正常 | emulator_id: 3
2025-07-28 06:43:06 - MainWindowV2 - WARNING - 模拟器3心跳状态更新未产生变化
2025-07-28 06:43:06 - FixedAsyncBridge - INFO - 异步操作完成: heartbeat_check
2025-07-28 06:43:07 - Emulator - INFO - Android系统启动完成 | emulator_id: 4 | elapsed_time: 9.9秒
2025-07-28 06:43:07 - InstagramFollowTaskManager - INFO - Instagram关注任务管理器收到模拟器4状态变化: 启动中 -> 运行中
2025-07-28 06:43:07 - InstagramFollowTaskManager - INFO - 启动模拟器4的Instagram关注任务线程 - 当前并发: 2/2
2025-07-28 06:43:07 - Emulator - INFO - 模拟器状态变化 | emulator_id: 4 | old_state: 启动中 | new_state: 运行中
2025-07-28 06:43:07 - Emulator - INFO - 模拟器启动成功 | emulator_id: 4 | running_count: 2
2025-07-28 06:43:07 - TaskActivityHeartbeatManager - INFO - 模拟器 4 已添加到任务活动监控，失败计数: 0
2025-07-28 06:43:07 - MainWindowV2 - INFO - 任务完成: 模拟器4, 任务start
2025-07-28 06:43:07 - MainWindowV2 - INFO - 模拟器4启动成功
2025-07-28 06:43:07 - InstagramTaskThread - INFO - [模拟器4] 开始等待启动完成
2025-07-28 06:43:07 - WindowArrangementManager - INFO - 收到模拟器启动信号: ID=4, 任务类型=start, 结果={'result': '启动成功', 'message': '启动完成'}
2025-07-28 06:43:07 - InstagramTaskThread - INFO - [模拟器4] ✅ 模拟器启动完成，等待时间: 0.0秒
2025-07-28 06:43:07 - InstagramTaskThread - INFO - [模拟器4] 开始窗口排列
2025-07-28 06:43:07 - WindowArrangementManager - INFO - 模拟器4启动完成，立即触发窗口排列
2025-07-28 06:43:07 - MainWindowV2 - WARNING - 未找到模拟器4，无法更新状态
2025-07-28 06:43:07 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
2025-07-28 06:43:07 - UnifiedEmulatorManager - INFO - 启动进度更新 | total: 3 | queued: 1 | starting: 0 | running: 2 | failed: 0 | cancelled: 0 | completed: 2 | percentage: 66.66666666666666 | concurrent_slots_used: 2 | max_concurrent: 2
2025-07-28 06:43:07 - MainWindowV2 - INFO - 启动进度: 排队1个, 启动中0个, 运行2个, 失败0个 (并发槽位:2/2)
2025-07-28 06:43:07 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中, PID: 18868
2025-07-28 06:43:07 - MainWindowV2 - INFO - 模拟器4: 启动中 -> 运行中
