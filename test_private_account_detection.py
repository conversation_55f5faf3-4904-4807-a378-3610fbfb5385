#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
私密账户检测专项测试
========================================
功能描述: 专门测试私密账户检测功能，验证XML获取和解析
测试目标: future__39 (私密账户)
创建时间: 2025-07-27
作者: AI Assistant
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入核心模块
from core.instagram_follow_task import InstagramFollowTask
from core.simple_config import get_config_manager
from core.logger_manager import log_info, log_error, log_warning


class PrivateAccountDetectionTester:
    """私密账户检测专项测试器"""

    def __init__(self, emulator_id: int = 2):
        """初始化测试器"""
        self.emulator_id = emulator_id
        self.config_manager = get_config_manager()
        self.follow_task = None

    async def setup_test_environment(self):
        """设置测试环境"""
        try:
            log_info(f"[测试器] 设置测试环境，模拟器ID: {self.emulator_id}")
            
            # 初始化Instagram关注任务
            self.follow_task = InstagramFollowTask(self.emulator_id)
            
            log_info(f"[测试器] ✅ 测试环境设置完成")
            return True
            
        except Exception as e:
            log_error(f"[测试器] ❌ 测试环境设置失败: {e}")
            return False

    async def test_private_account_detection(self):
        """测试私密账户检测功能"""
        try:
            log_info(f"[测试器] 🔍 开始测试私密账户检测功能")
            
            # 调用私密账户检测方法
            is_private = await self.follow_task.is_private_account()
            
            log_info(f"[测试器] 🔍 私密账户检测结果: {is_private}")
            
            # 检查是否生成了调试XML文件
            debug_xml_file = f"debug_xml_{self.emulator_id}.xml"
            if Path(debug_xml_file).exists():
                log_info(f"[测试器] 📄 调试XML文件已生成: {debug_xml_file}")
                
                # 读取并显示XML文件大小
                xml_size = Path(debug_xml_file).stat().st_size
                log_info(f"[测试器] 📄 XML文件大小: {xml_size} 字节")
                
                # 读取XML内容的前500字符
                with open(debug_xml_file, 'r', encoding='utf-8') as f:
                    xml_preview = f.read(500)
                    log_info(f"[测试器] 📄 XML内容预览: {repr(xml_preview)}")
            else:
                log_warning(f"[测试器] ⚠️ 调试XML文件未生成")
            
            return is_private
            
        except Exception as e:
            log_error(f"[测试器] ❌ 私密账户检测测试失败: {e}")
            return None

    async def run_test(self):
        """运行完整测试"""
        try:
            log_info("=" * 80)
            log_info("私密账户检测专项测试开始")
            log_info("=" * 80)
            
            # 设置测试环境
            if not await self.setup_test_environment():
                return False
            
            # 测试私密账户检测
            result = await self.test_private_account_detection()
            
            log_info("=" * 80)
            log_info(f"私密账户检测专项测试完成")
            log_info(f"检测结果: {result}")
            log_info("=" * 80)
            
            return result is not None
            
        except Exception as e:
            log_error(f"[测试器] ❌ 测试运行失败: {e}")
            return False


async def main():
    """主函数"""
    tester = PrivateAccountDetectionTester(emulator_id=2)
    success = await tester.run_test()
    
    if success:
        print("✅ 测试完成")
    else:
        print("❌ 测试失败")


if __name__ == "__main__":
    asyncio.run(main())
