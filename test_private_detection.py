#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试私密用户检测
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from core.instagram_follow_task import InstagramFollowTask
from core.config_manager import ConfigManager
from core.ld_api import LDApi

async def test_private_detection():
    """测试私密用户检测"""
    
    print("🎯 测试私密用户检测...")
    print("=" * 50)
    
    # 初始化
    config_manager = ConfigManager()
    ld = LDApi()
    ld.emulator_id = 2
    
    task = InstagramFollowTask(2, config_manager, ld)
    
    # 跳转到future__39用户页面
    print("📱 跳转到用户页面: future__39")
    await task.navigate_to_profile("future__39")
    
    # 等待页面加载
    await asyncio.sleep(3)
    
    # 获取UI XML
    print("📄 获取UI XML内容...")
    success, xml_content = ld.execute_ld_with_multilingual_support(2, "uiautomator dump /sdcard/ui_test.xml")
    
    if success and xml_content:
        print("✅ XML获取成功")
        
        # 查找包含"私"、"帐户"、"Private"等关键字的文本
        import xml.etree.ElementTree as ET
        root = ET.fromstring(xml_content)
        
        print("\n🔍 查找可能的私密标识文本:")
        found_texts = []
        
        for elem in root.iter():
            elem_text = elem.get('text', '').strip()
            if elem_text and ('私' in elem_text or '帐户' in elem_text or 'Private' in elem_text or '账户' in elem_text):
                found_texts.append(elem_text)
                print(f"  📝 找到文本: '{elem_text}'")
        
        # 测试当前的检测逻辑
        print("\n🧪 测试当前检测逻辑:")
        private_keywords = ["私密账户", "私密账号", "私人帐户", "Private"]
        
        for text in found_texts:
            for keyword in private_keywords:
                if keyword in text:
                    print(f"  ✅ 匹配成功: '{text}' 包含 '{keyword}'")
                    break
            else:
                print(f"  ❌ 匹配失败: '{text}' 不包含任何关键词")
        
        # 测试批量检测
        print("\n🔬 测试批量检测结果:")
        profile_data = await task._direct_follow_batch_detect("future__39")
        print(f"  is_private: {profile_data.get('is_private')}")
        print(f"  is_verified: {profile_data.get('is_verified')}")
        print(f"  is_followed: {profile_data.get('is_followed')}")
        
    else:
        print("❌ XML获取失败")

if __name__ == "__main__":
    asyncio.run(test_private_detection())
